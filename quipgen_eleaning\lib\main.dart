import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:quipgen_eleaning/firebase_options.dart';
import 'package:quipgen_eleaning/view/auth/auth_wrapper.dart';
import 'package:quipgen_eleaning/view/auth/profile_screen.dart';
import 'package:quipgen_eleaning/view/home/<USER>';
import 'package:quipgen_eleaning/view/onboarding/onboarding_screen.dart';
import 'package:quipgen_eleaning/view/login/signup_screen.dart';
import 'package:quipgen_eleaning/view/login/login_screen.dart';
import 'package:quipgen_eleaning/view/login/phone_signup_screen.dart';
// import 'package:quipgen_eleaning/view/login/otp_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase (automatic initialization is disabled in AndroidManifest.xml)
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Set system UI overlay style for consistent status bar appearance
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ),
  );

  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Define theme colors
    const Color primaryColor = Color.fromARGB(
      255,
      166,
      33,
      243,
    ); // Purple color from onboarding
    const Color secondaryColor = Color.fromARGB(
      255,
      17,
      17,
      17,
    ); // Black color from buttons

    return MaterialApp(
      theme: ThemeData(
        primaryColor: primaryColor,
        scaffoldBackgroundColor: Colors.white,
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: secondaryColor,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.light,
          ),
        ),
        colorScheme: const ColorScheme.light(
          primary: primaryColor,
          secondary: secondaryColor,
          surface: Colors.white,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: secondaryColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        useMaterial3: true,
      ),
      darkTheme: ThemeData(
        primaryColor: primaryColor,
        scaffoldBackgroundColor: Colors.grey[900],
        appBarTheme: AppBarTheme(
          backgroundColor: Colors.grey[900],
          foregroundColor: Colors.white,
          elevation: 0,
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.light,
            statusBarBrightness: Brightness.dark,
          ),
        ),
        colorScheme: ColorScheme.dark(
          primary: primaryColor,
          secondary: secondaryColor,
          surface: Colors.grey[850]!,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: primaryColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        useMaterial3: true,
      ),
      themeMode: ThemeMode.system, // This will follow the device theme
      debugShowCheckedModeBanner: false,
      initialRoute: '/',
      routes: {
        '/': (context) => const AppScaffold(child: AuthWrapper()),
        '/onboarding': (context) => const OnboardingScreen(),
        '/home': (context) => const AppScaffold(child: HomeScreen()),
        '/signup': (context) => const SignupScreen(),
        '/login': (context) => const LoginScreen(),
        '/phone-signup': (context) => const PhoneSignUpScreen(),
        '/profile': (context) => const ProfileScreen(),
      },
    );
  }
}

class AppScaffold extends StatefulWidget {
  final Widget child;

  const AppScaffold({super.key, required this.child});

  @override
  State<AppScaffold> createState() => _AppScaffoldState();
}

class _AppScaffoldState extends State<AppScaffold>
    with SingleTickerProviderStateMixin {
  int _selectedIndex = 0; // Default to Home tab
  late AnimationController _controller;

  final List<Widget> _screens = [
    const HomeScreen(),
    const Center(child: Text('Courses Coming Soon')),
    const Center(child: Text('Library Coming Soon')),
    const Center(child: Text('Videos Coming Soon')),
    const ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Only show bottom navigation on authenticated screens
    final bool showBottomNav = ModalRoute.of(context)?.settings.name == '/home';

    return PopScope(
      canPop: !(showBottomNav && _selectedIndex != 0),
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          setState(() {
            _selectedIndex = 0;
          });
        }
      },
      child: Scaffold(
        body: showBottomNav
            ? IndexedStack(index: _selectedIndex, children: _screens)
            : widget.child,
        bottomNavigationBar: showBottomNav
            ? Container(
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.2),
                      spreadRadius: 0,
                      blurRadius: 10,
                      offset: const Offset(0, -3),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  child: BottomAppBar(
                    color: Colors.white,
                    elevation: 0,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10.0,
                        vertical: 8.0,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildNavItem(
                            0,
                            Icons.home,
                            'Home',
                            // onPressed: () {
                            //   Navigator.pushNamed(context, '/home');
                            // },
                          ),
                          _buildNavItem(1, Icons.menu_book_rounded, 'Courses'),
                          _buildNavItem(2, Icons.card_giftcard, 'Library'),
                          _buildNavItem(3, Icons.video_camera_front, 'Videos'),
                          _buildNavItem(
                            4,
                            Icons.person,
                            'Profile',
                            // onPressed: () {
                            //   Navigator.pushReplacementNamed(context, '/profile');
                            // },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              )
            : null,
      ),
    );
  }

  Widget _buildNavItem(int index, IconData icon, String label) {
    final bool isSelected = _selectedIndex == index;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(20),
        onTap: () {
          if (!isSelected) {
            setState(() {
              _selectedIndex = index;
            });
            _controller.reset();
            _controller.forward();

            // Call the onPressed function if provided
            // if (onPressed != null) {
            //   onPressed();
            // }
          }
        },
        splashColor: Colors.grey.withValues(alpha: 0.1),
        highlightColor: Colors.transparent,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? Colors.black : Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 1.0, end: isSelected ? 1.2 : 1.0),
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeOutCubic,
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: value,
                    child: Icon(
                      icon,
                      color: isSelected ? Colors.white : Colors.grey,
                      size: 24,
                    ),
                  );
                },
              ),
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOutCubic,
                width: isSelected ? 8.0 : 0.0,
              ),
              ClipRect(
                child: AnimatedSize(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOutCubic,
                  child: isSelected
                      ? Text(
                          label,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
